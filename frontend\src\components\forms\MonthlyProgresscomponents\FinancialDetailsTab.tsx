import React from "react";
import { MonthlyProgressSchemaType } from "../../../schemas/monthlyProgress/MonthlyProgressSchema";
import { Controller, useFormContext } from "react-hook-form";
import { Box, FormControl, Grid, Paper, TextField, Typography } from "@mui/material";

const FinancialDetailsTab: React.FC = () => {
    const { control, formState: { errors }, watch } = useFormContext<MonthlyProgressSchemaType>();
    
    
    // Watch for calculation fields
    const net = watch("net");
    const serviceTax = watch("serviceTax");
    const odcs = watch("odcs");
    const staff = watch("staff");

    // Auto-calculate totals (you might want to implement this logic)
    React.useEffect(() => {
        if (net && serviceTax) {
            // Calculate fee total
        }
        if (odcs && staff) {
            // Calculate budget subtotal
        }
    }, [net, serviceTax, odcs, staff]);

    return (
        <Box sx={{ flexGrow: 1 }}>
            <Grid container spacing={3}>
                {/* Fees Section */}
                <Grid item xs={12} md={6} >
                    <Paper elevation={1} sx={{ p: 3, backgroundColor: '#f5f5f5' }} >
                        <Typography variant="h6" gutterBottom color="primary">
                            Fees
                        </Typography>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <Controller
                                    name="net"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            fullWidth
                                            type="text"
                                            label="Net Amount"
                                            {...field}
                                            error={!!errors.net}
                                            helperText={errors.net?.message || ''}
                                            value={field.value || ''}
                                            onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <Controller
                                    name="serviceTax"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            fullWidth
                                            label="Service Tax (%)"
                                            type="text"
                                            error={!!errors.serviceTax}
                                            helperText={errors.serviceTax?.message || ''}
                                            {...field}
                                            value={field.value || ''}
                                            onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                                            inputProps={{ min: 0, max: 100, step: 0.01 }}
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <Controller
                                    name="feeTotal"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            fullWidth
                                            label="Total Fee"
                                            type="text"
                                            error={!!errors.feeTotal}
                                            helperText={errors.feeTotal?.message || ''}
                                            {...field}
                                            value={field.value || ''}
                                            onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                                        />
                                    )}
                                />
                            </Grid>
                        </Grid>
                    </Paper>
                </Grid>

                {/* Budget Costs Section */}
                <Grid item xs={12} md={6}>
                    <Paper elevation={1} sx={{ p: 3 }}>
                        <Typography variant="h6" gutterBottom color="primary">
                            Budget Costs
                        </Typography>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <Controller
                                    name="odcs"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            fullWidth
                                            label="ODCs (Other Direct Costs)"
                                            type="text"
                                            error={!!errors.odcs}
                                            helperText={errors.odcs?.message || ''}
                                            {...field}
                                            value={field.value || ''}
                                            onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <Controller
                                    name="staff"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            fullWidth
                                            label="Staff Costs"
                                            type="text"
                                            error={!!errors.staff}
                                            helperText={errors.staff?.message || ''}
                                            {...field}
                                            value={field.value || ''}
                                            onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <Controller
                                    name="BudgetSubTotal"
                                    control={control}
                                    render={({ field }) => (
                                        <TextField
                                            fullWidth
                                            label="Budget Sub Total"
                                            type="text"
                                            error={!!errors.BudgetSubTotal}
                                            helperText={errors.BudgetSubTotal?.message || ''}
                                            {...field}
                                            value={field.value || ''}
                                            onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                                        />
                                    )}
                                />
                            </Grid>
                        </Grid>
                    </Paper>
                </Grid>
            </Grid>
        </Box>
  );
};

export default FinancialDetailsTab;
