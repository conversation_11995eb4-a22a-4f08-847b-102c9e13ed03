{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Jwt": {"Key": "7bf578ef918fcccd26725d646385b72c95d29c01b38abc79caec1dbc4a36d2f5", "Issuer": "your-app-name", "Audience": "your-app-name"}, "AllowedHosts": "*", "ConnectionStrings": {"AppDbConnection": "Server=SERVER2016;Database=NJSAPIProjectManagement;User id=sa;password=**********;TrustServerCertificate=True;Trusted_Connection=True"}, "Cors": {"AllowedOrigins": ["http://localhost:5173", "http://localhost:5245", "http://localhost:5176", "http://**************:5179", "http://**************:5176"]}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "Port": 587, "Username": "<EMAIL>", "Password": "grstgibxcsxhjyrz", "FromEmail": "<EMAIL>", "FromName": "NJS Project Management(Dev)", "EnableSsl": true, "EnableEmailNotifications": true}}