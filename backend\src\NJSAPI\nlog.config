<?xml version="1.0" encoding="utf-8"?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <targets>
    <!-- General log file target -->
    <target name="allfile" xsi:type="AsyncWrapper" queueLimit="10000" overflowAction="Discard" batchSize="200"
            timeToSleepBetweenBatches="1">
      <target name="logfile" xsi:type="File"
              archiveAboveSize="1000000000"
              maxArchiveFiles="50"
              archiveNumbering="DateAndSequence"
              archiveDateFormat="yyyy-MM-dd"
              archiveEvery="Day"
              archiveFileName="${basedir}/logs/njs-${shortdate}.{#}.log"
              fileName="${basedir}/logs/njs.log">
        <layout xsi:type="JsonLayout" includeAllProperties="true">
          <attribute name="@timestamp" layout="${longdate}" />
          <attribute name="@level" layout="${level:upperCase=true}" />
          <attribute name="@logger" layout="${logger:shortName=true}" />
          <attribute name="@message" layout="${message}" />
          <attribute name="@exception" layout="${exception:format=tostring}" />
        </layout>
      </target>
    </target>

    <!-- Email-specific log file target -->
    <target name="emailfile" xsi:type="AsyncWrapper" queueLimit="10000" overflowAction="Discard" batchSize="200"
            timeToSleepBetweenBatches="1">
      <target name="emaillogfile" xsi:type="File"
              archiveAboveSize="1000000000"
              maxArchiveFiles="50"
              archiveNumbering="DateAndSequence"
              archiveDateFormat="yyyy-MM-dd"
              archiveEvery="Day"
              archiveFileName="${basedir}/logs/email-${shortdate}.{#}.log"
              fileName="${basedir}/logs/email.log">
        <layout xsi:type="JsonLayout" includeAllProperties="true" suppressSpaces="true">
          <attribute name="@timestamp" layout="${longdate}" />
          <attribute name="@level" layout="${level:upperCase=true}" />
          <attribute name="@logger" layout="${logger:shortName=true}" />
          <attribute name="@correlationId" layout="${mdlc:item=CorrelationId}" />
          <attribute name="@exception" layout="${exception:format=tostring}" />
          <attribute name="@message" encode="false" layout="${event-properties:item=@message}" />
          <attribute name="@sentBodyMessage" encode="false" layout="${event-properties:item=@sentBodyMessage}" />
        </layout>
      </target>
    </target>

    <!-- Console Target -->
    <target xsi:type="Console" name="lifetimeConsole" 
            layout="${MicrosoftConsoleLayout}" />
  </targets>

  <rules>
    <!-- Ignore non-critical logs -->
    <logger name="System.Net.Http.HttpClient.*" maxLevel="Warn" final="true" />
    
    <!-- Email service logs -->
    <logger name="NJS.Application.Services.EmailService" minlevel="Info" writeTo="emailfile">
      <filters defaultAction='Log'>
        <when condition="contains('${aspnet-request-url}','/health/')" action="Ignore" />
        <when condition="contains('${exceptionType}', 'System.Threading.Tasks.TaskCanceledException') and contains('${logger}', 'Kestrel')"
              action="Ignore" />
      </filters>
    </logger>

    <!-- All other logs -->
    <logger name="*" writeTo="allfile">
      <filters defaultAction='Log'>
        <when condition="contains('${aspnet-request-url}','/health/')" action="Ignore" />
        <when condition="contains('${exceptionType}', 'System.Threading.Tasks.TaskCanceledException') and contains('${logger}', 'Kestrel')"
              action="Ignore" />
      </filters>
    </logger>

    <!-- Console logs -->
    <logger name="Microsoft.Hosting.Lifetime" minlevel="Info" writeTo="lifetimeConsole" />
  </rules>
</nlog>
