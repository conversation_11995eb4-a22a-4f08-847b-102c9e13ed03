{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Error"}}, "Jwt": {"Key": "LongSecureRandomKeyForProductionEnvironmentMustBeReplacedWithActualSecureKey", "Issuer": "NJSProjectManagementProd", "Audience": "NJSProjectManagementProdClient", "ExpirationInHours": 1}, "IdentitySettings": {"PasswordPolicy": {"RequireDigit": true, "RequireLowercase": true, "RequireNonAlphanumeric": true, "RequireUppercase": true, "RequiredLength": 12, "RequiredUniqueChars": 3}, "LockoutSettings": {"DefaultLockoutTimespan": 30, "MaxFailedAccessAttempts": 5, "AllowedForNewUsers": true}, "UserSettings": {"RequireUniqueEmail": true, "AllowedUserNameCharacters": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+"}}, "ConnectionStrings": {"DefaultConnection": "Server=your-production-server;Database=NJSAPIProjectManagementProd;User Id=your-prod-user;Password=your-secure-password;TrustServerCertificate=False;Encrypt=True;MultipleActiveResultSets=true"}, "AllowedHosts": "*", "CorsSettings": {"AllowedOrigins": ["https://njs-project-management.com", "https://www.njs-project-management.com"]}, "ProductionFeatures": {"EnableSwagger": false, "DetailedErrors": false, "SeedTestData": false}, "SecuritySettings": {"EnableHttpsRedirection": true, "StrictTransportSecurity": {"Enabled": true, "MaxAge": 31536000, "IncludeSubDomains": true, "Preload": false}, "XssProtection": {"Enabled": true, "Mode": "block"}}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "Port": 587, "Username": "<EMAIL>", "Password": "your-production-app-specific-password", "FromEmail": "<EMAIL>", "FromName": "NJS Project Management", "EnableSsl": true, "EnableEmailNotifications": false}}