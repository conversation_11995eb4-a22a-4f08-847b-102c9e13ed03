import React from "react";
import { MonthlyProgressSchemaType } from "../../../schemas/monthlyProgress/MonthlyProgressSchema";
import { Controller, useFormContext } from "react-hook-form";
import { Box, Grid, Paper, TextField, Typography } from "@mui/material";

const ContractAndCostsTab: React.FC = () => {
  const { control, formState: { errors } } = useFormContext<MonthlyProgressSchemaType>();

  return (
    // <Box sx={{ flexGrow: 1 }}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom color="primary">
              Contract Type
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Controller
                  name="contractType.lumpsum"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="Lumpsum"
                      type="checkbox"
                      {...field}
                      error={!!errors.contractType?.lumpsum}
                      helperText={errors.contractType?.lumpsum?.message || ''}
                    />
                    )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  name="contractType.tAndE"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="Time & Expense"
                      type="checkbox"
                      {...field}
                      error={!!errors.contractType?.tAndE}
                      helperText={errors.contractType?.tAndE?.message || ''}
                    />
                    )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  name="contractType.percentage"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="Percentage"
                      type="text"
                      {...field}
                      error={!!errors.contractType?.percentage}
                      helperText={errors.contractType?.percentage?.message || ''}
                    />
                    )}
                />
              </Grid>
            </Grid>
            <Typography variant="h6" gutterBottom color="primary">
              Actual Costs
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Controller
                  name="actualCosts.odcs"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="ODCs"
                      type="text"
                      {...field}
                      error={!!errors.actualCosts?.odcs}
                      helperText={errors.actualCosts?.odcs?.message || ''}
                    />
                    )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  name="actualCosts.staff"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="Staff"
                      type="text"
                      {...field}
                      error={!!errors.actualCosts?.staff}
                      helperText={errors.actualCosts?.staff?.message || ''}
                    />
                    )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  name="actualCosts.subtotal"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="Subtotal"
                      type="text"
                      {...field}
                      error={!!errors.actualCosts?.subtotal}
                      helperText={errors.actualCosts?.subtotal?.message || ''}
                    />
                    )}
                />
              </Grid>
            </Grid>
            </Paper>
        </Grid>
      </Grid>
    // </Box>
  );
};

export default ContractAndCostsTab;
